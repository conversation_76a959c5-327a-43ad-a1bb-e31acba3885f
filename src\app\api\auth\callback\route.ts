import { NextRequest, NextResponse } from 'next/server';
import { getAuthorizedClient } from '@/lib/gmail';

export async function POST(request: NextRequest) {
  try {
    const { code } = await request.json();
    
    if (!code) {
      return NextResponse.json(
        { success: false, error: 'Authorization code is required' },
        { status: 400 }
      );
    }

    // Exchange authorization code for tokens
    await getAuthorizedClient(code);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Auth callback error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to authenticate' },
      { status: 500 }
    );
  }
}
