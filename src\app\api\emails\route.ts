import { NextRequest, NextResponse } from 'next/server';
import { getAuthorizedClient, listMessages } from '@/lib/gmail';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const maxResults = parseInt(searchParams.get('maxResults') || '10');
    
    // Get authorized client
    const auth = await getAuthorizedClient();
    
    // Fetch messages
    const messages = await listMessages(auth, maxResults);
    
    return NextResponse.json({ success: true, messages });
  } catch (error) {
    console.error('Error fetching emails:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch emails' },
      { status: 500 }
    );
  }
}
