import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import fs from 'fs';
import path from 'path';

// Gmail API scopes
const SCOPES = ['https://www.googleapis.com/auth/gmail.readonly'];

// Path to credentials file
const CREDENTIALS_PATH = path.join(process.cwd(), 'cert', 'google_oauth2.json');
const TOKEN_PATH = path.join(process.cwd(), 'cert', 'token.json');

export interface EmailMessage {
  id: string;
  threadId: string;
  subject: string;
  from: string;
  to: string;
  date: string;
  snippet: string;
  body?: string;
  isRead: boolean;
}

/**
 * Load client secrets from a local file.
 */
function loadCredentials() {
  try {
    const content = fs.readFileSync(CREDENTIALS_PATH, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error('Error loading credentials:', error);
    throw new Error('Unable to load credentials file');
  }
}

/**
 * Create an OAuth2 client with the given credentials
 */
function createOAuth2Client() {
  const credentials = loadCredentials();
  const { client_secret, client_id, redirect_uris } = credentials.web || credentials.installed;

  // Use localhost redirect URI for development
  const redirectUri = 'http://localhost:9042/auth/callback';

  return new google.auth.OAuth2(client_id, client_secret, redirectUri);
}

/**
 * Get and store new token after prompting for user authorization
 */
export function getAuthUrl(): string {
  const oAuth2Client = createOAuth2Client();

  const authUrl = oAuth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
  });

  return authUrl;
}

/**
 * Store token to disk
 */
function storeToken(token: any) {
  try {
    fs.writeFileSync(TOKEN_PATH, JSON.stringify(token));
    console.log('Token stored to', TOKEN_PATH);
  } catch (error) {
    console.error('Error storing token:', error);
  }
}

/**
 * Load token from disk
 */
function loadToken() {
  try {
    const token = fs.readFileSync(TOKEN_PATH, 'utf8');
    return JSON.parse(token);
  } catch (error) {
    return null;
  }
}

/**
 * Get authorized OAuth2 client
 */
export async function getAuthorizedClient(authCode?: string): Promise<OAuth2Client> {
  const oAuth2Client = createOAuth2Client();

  // Check if we have a stored token
  const token = loadToken();

  if (token) {
    oAuth2Client.setCredentials(token);
    return oAuth2Client;
  }

  if (authCode) {
    try {
      const { tokens } = await oAuth2Client.getToken(authCode);
      oAuth2Client.setCredentials(tokens);
      storeToken(tokens);
      return oAuth2Client;
    } catch (error) {
      console.error('Error retrieving access token:', error);
      throw new Error('Failed to get access token');
    }
  }

  throw new Error('No valid credentials available');
}

/**
 * List messages from Gmail
 */
export async function listMessages(auth: OAuth2Client, maxResults: number = 10): Promise<EmailMessage[]> {
  const gmail = google.gmail({ version: 'v1', auth });

  try {
    const response = await gmail.users.messages.list({
      userId: 'me',
      maxResults,
    });

    const messages = response.data.messages || [];
    const emailMessages: EmailMessage[] = [];

    // Get detailed information for each message
    for (const message of messages) {
      if (message.id) {
        const messageDetail = await gmail.users.messages.get({
          userId: 'me',
          id: message.id,
        });

        const msg = messageDetail.data;
        const headers = msg.payload?.headers || [];

        const subject = headers.find(h => h.name === 'Subject')?.value || 'No Subject';
        const from = headers.find(h => h.name === 'From')?.value || 'Unknown Sender';
        const to = headers.find(h => h.name === 'To')?.value || 'Unknown Recipient';
        const date = headers.find(h => h.name === 'Date')?.value || '';

        const isRead = !msg.labelIds?.includes('UNREAD');

        emailMessages.push({
          id: msg.id || '',
          threadId: msg.threadId || '',
          subject,
          from,
          to,
          date,
          snippet: msg.snippet || '',
          isRead,
        });
      }
    }

    return emailMessages;
  } catch (error) {
    console.error('Error listing messages:', error);
    throw new Error('Failed to fetch messages');
  }
}

/**
 * Get message body
 */
export async function getMessageBody(auth: OAuth2Client, messageId: string): Promise<string> {
  const gmail = google.gmail({ version: 'v1', auth });

  try {
    const response = await gmail.users.messages.get({
      userId: 'me',
      id: messageId,
    });

    const msg = response.data;
    let body = '';

    function extractBody(payload: any): string {
      if (payload.body?.data) {
        return Buffer.from(payload.body.data, 'base64').toString('utf-8');
      }

      if (payload.parts) {
        for (const part of payload.parts) {
          const partBody = extractBody(part);
          if (partBody) {
            body += partBody;
          }
        }
      }

      return body;
    }

    return extractBody(msg.payload);
  } catch (error) {
    console.error('Error getting message body:', error);
    throw new Error('Failed to fetch message body');
  }
}
