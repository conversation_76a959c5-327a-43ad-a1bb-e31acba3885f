"use client";

import { useState, useEffect } from "react";
import { getAuthUrl } from "@/lib/gmail";

interface EmailMessage {
  id: string;
  threadId: string;
  subject: string;
  from: string;
  to: string;
  date: string;
  snippet: string;
  body?: string;
  isRead: boolean;
}

export default function Home() {
  const [emails, setEmails] = useState<EmailMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [authenticated, setAuthenticated] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState<EmailMessage | null>(null);
  const [emailBody, setEmailBody] = useState<string>("");

  // Check if user is authenticated on component mount
  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      const response = await fetch("/api/emails?maxResults=1");
      if (response.ok) {
        setAuthenticated(true);
        loadEmails();
      }
    } catch (error) {
      console.log("Not authenticated yet");
    }
  };

  const handleAuth = () => {
    const authUrl = getAuthUrl();
    window.location.href = authUrl;
  };

  const loadEmails = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/emails?maxResults=20");
      const data = await response.json();

      if (data.success) {
        setEmails(data.messages);
        setAuthenticated(true);
      } else {
        console.error("Failed to load emails:", data.error);
      }
    } catch (error) {
      console.error("Error loading emails:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadEmailBody = async (emailId: string) => {
    try {
      const response = await fetch(`/api/emails/${emailId}`);
      const data = await response.json();

      if (data.success) {
        setEmailBody(data.body);
      }
    } catch (error) {
      console.error("Error loading email body:", error);
    }
  };

  const handleEmailClick = (email: EmailMessage) => {
    setSelectedEmail(email);
    loadEmailBody(email.id);
  };

  if (!authenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Gmail Reader
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Connect your Gmail account to read emails
            </p>
          </div>
          <div className="mt-8 space-y-6">
            <button
              onClick={handleAuth}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Connect Gmail Account
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Gmail Reader</h1>
            <button
              onClick={loadEmails}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
            >
              {loading ? "Loading..." : "Refresh"}
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Email List */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Emails ({emails.length})
                </h3>
              </div>
              <ul className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
                {emails.map((email) => (
                  <li
                    key={email.id}
                    className={`px-4 py-4 hover:bg-gray-50 cursor-pointer ${
                      selectedEmail?.id === email.id ? "bg-blue-50" : ""
                    }`}
                    onClick={() => handleEmailClick(email)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center">
                          <p
                            className={`text-sm font-medium text-gray-900 truncate ${
                              !email.isRead ? "font-bold" : ""
                            }`}
                          >
                            {email.subject}
                          </p>
                          {!email.isRead && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              New
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 truncate">
                          From: {email.from}
                        </p>
                        <p className="text-sm text-gray-400 truncate">
                          {email.snippet}
                        </p>
                      </div>
                      <div className="ml-2 flex-shrink-0 text-sm text-gray-500">
                        {new Date(email.date).toLocaleDateString()}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>

            {/* Email Detail */}
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              {selectedEmail ? (
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    {selectedEmail.subject}
                  </h3>
                  <div className="mb-4 text-sm text-gray-600">
                    <p>
                      <strong>From:</strong> {selectedEmail.from}
                    </p>
                    <p>
                      <strong>To:</strong> {selectedEmail.to}
                    </p>
                    <p>
                      <strong>Date:</strong>{" "}
                      {new Date(selectedEmail.date).toLocaleString()}
                    </p>
                  </div>
                  <div className="border-t pt-4">
                    <div
                      className="prose max-w-none text-sm"
                      dangerouslySetInnerHTML={{
                        __html: emailBody || selectedEmail.snippet,
                      }}
                    />
                  </div>
                </div>
              ) : (
                <div className="px-4 py-5 sm:p-6 text-center text-gray-500">
                  Select an email to view its content
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
