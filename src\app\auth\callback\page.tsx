import { getAuthorizedClient } from "@/lib/gmail";
import { redirect } from "next/navigation";

interface AuthCallbackProps {
  searchParams: { code?: string; error?: string };
}

export default async function AuthCallback({
  searchParams,
}: AuthCallbackProps) {
  const { code, error } = searchParams;

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Gmail Authentication
            </h2>
            <p className="mt-2 text-sm text-red-600">
              Authentication failed: {error}
            </p>
            <div className="mt-4">
              <a
                href="/auth/login"
                className="text-blue-600 hover:text-blue-500"
              >
                Try again
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (code) {
    try {
      await getAuthorizedClient(code);
      redirect("/");
    } catch (authError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full space-y-8">
            <div className="text-center">
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                Gmail Authentication
              </h2>
              <p className="mt-2 text-sm text-red-600">
                Authentication failed:{" "}
                {authError instanceof Error
                  ? authError.message
                  : "Unknown error"}
              </p>
              <div className="mt-4">
                <a
                  href="/auth/login"
                  className="text-blue-600 hover:text-blue-500"
                >
                  Try again
                </a>
              </div>
            </div>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Gmail Authentication
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            No authorization code received
          </p>
          <div className="mt-4">
            <a
              href="/auth/login"
              className="text-blue-600 hover:text-blue-500"
            >
              Try again
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
