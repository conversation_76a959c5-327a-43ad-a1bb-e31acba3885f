'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function AuthCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState('Processing...');

  useEffect(() => {
    const code = searchParams.get('code');
    const error = searchParams.get('error');

    if (error) {
      setStatus(`Authentication failed: ${error}`);
      return;
    }

    if (code) {
      // Send the authorization code to our API
      fetch('/api/auth/callback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code }),
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            setStatus('Authentication successful! Redirecting...');
            setTimeout(() => {
              router.push('/');
            }, 2000);
          } else {
            setStatus(`Authentication failed: ${data.error}`);
          }
        })
        .catch(error => {
          setStatus(`Error: ${error.message}`);
        });
    } else {
      setStatus('No authorization code received');
    }
  }, [searchParams, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Gmail Authentication
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {status}
          </p>
        </div>
      </div>
    </div>
  );
}
