import { NextRequest, NextResponse } from 'next/server';
import { getAuthorizedClient, getMessageBody } from '@/lib/gmail';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: messageId } = await params;

    if (!messageId) {
      return NextResponse.json(
        { success: false, error: 'Message ID is required' },
        { status: 400 }
      );
    }

    // Get authorized client
    const auth = await getAuthorizedClient();

    // Fetch message body
    const body = await getMessageBody(auth, messageId);

    return NextResponse.json({ success: true, body });
  } catch (error) {
    console.error('Error fetching email body:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch email body' },
      { status: 500 }
    );
  }
}
